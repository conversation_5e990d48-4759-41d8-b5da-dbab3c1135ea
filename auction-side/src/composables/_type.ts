// Type definitions for API responses and data structures
type BidStatus = {
  current_price: number
  bid_price: number
  bid_quantity: number
  tax_rate: number
  pitch_width?: number
  is_top_member?: boolean | null
  minimum_bid_exceeded?: boolean
}

type AttentionInfo = {
  bid_count: number
  favorited_count?: number
  view_count?: number
  is_favorited?: boolean
}

type FreeField = {
  product_name?: string
  image_url?: string
  start_price?: number
  category?: string
  condition?: string
  shipping_free?: boolean
  instant_price?: number
  minimum_bid_price?: number
}

type BidHistory = {
  bid_price: number
  bid_quantity: number
  bid_datetime: string
  user_id?: string
}

type RawAuctionItem = {
  exhibition_item_no: string
  item_no: string
  category_id: number
  auction_classification?: number
  sold_out?: boolean
  free_field: FreeField
  bid_status: BidStatus
  attention_info: AttentionInfo
  start_datetime: string
  end_datetime: string
  exhibition_no: string
  bid_histories?: BidHistory[]
  status?: string
}

type FormattedAuctionItem = RawAuctionItem & {
  link: string
  imgSrc: string
  currentPrice: string
  currentPriceTaxIncluded: string
  noOfBids: number
  endDatePart: string
  endTimePart: string
  startDatePart: string
  startTimePart: string
  bidPrice: string
  bidQuantity: string
  bidInputError: {
    bidPrice: string | null
    bidQuantity: string | null
  }
}

type ExhibitionGroup = {
  exhibition_no: string
  exhibition_name: string
  start_datetime: string
  end_datetime: string
}

type ProductList = {
  all: FormattedAuctionItem[]
  exhibitionList: ExhibitionGroup[]
}

type ProductDetails = {
  exhibition_item_no: string
  item_no: string
  category_id: number
  free_field: FreeField
  bid_status: BidStatus
  attention_info: AttentionInfo
  start_datetime: string
  end_datetime: string
  exhibition_no: string
  bid_histories: BidHistory[]
  favorite_count: number
  currentPrice: string
  currentPriceTaxIncluded: string
  bid_count: number
  endDatePart: string
  endTimePart: string
  images: string[]
  freeFields: FreeField
  productName: string
}

export type {AttentionInfo, BidHistory, BidStatus, ExhibitionGroup, FormattedAuctionItem, FreeField, ProductDetails, ProductList, RawAuctionItem}
