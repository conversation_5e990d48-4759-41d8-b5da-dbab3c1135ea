<script setup>
  import {computed} from 'vue'
  import {useSearchResultStore} from '../../stores/search-results'
  import ProductList from '../search-list/ProductList.vue'

  const searchStore = useSearchResultStore()

  const productList = computed(() => searchStore.productList.all)
  const exhibitionList = computed(() => searchStore.productList.exhibitionList)
</script>
<template>
  <section id="list-auction">
    <div class="container">
      <template v-for="exh in exhibitionList" :key="exh.exhibition_no">
        <ProductList
          v-if="productList.some(x => x.exhibition_no === exh.exhibition_no)"
          :productList="productList.filter(x => x.exhibition_no === exh.exhibition_no)"
          :exhibitionInfo="exh"
        />
      </template>
    </div>
  </section>
</template>
