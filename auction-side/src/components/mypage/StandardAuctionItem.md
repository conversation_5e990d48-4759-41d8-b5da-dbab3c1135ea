Refactor the Vue component `auction-side/src/components/mypage/StandardAuctionItem.vue` to display individual auction items using real data from the backend API instead of hardcoded HTML content.

**Requirements:**

1. **Component Structure & Props:**

   - Change from displaying multiple hardcoded `<li>` elements to rendering a single auction item
   - Accept an `item` prop of type `FormattedAuctionItem` (defined in `auction-side/src/composables/_type.ts`)
   - Accept a `@refresh` event emitter for parent component communication
   - Remove the current `productList` and `exhibitionList` props since this will now be a single-item component
   - Remove 

2. **Parent Component Integration:**

   - Update `auction-side/src/components/mypage/_MyPage.vue` to use the v-for pattern:

   ```vue
   <StandardAuctionItem v-for="item in productList" :key="item.exhibition_item_no" :item="item" @refresh="refreshList" />
   ```

3. **Data Binding & Layout Preservation:**

   - Maintain the existing CSS classes and visual layout structure
   - Replace hardcoded content with dynamic data from the `item` prop:
     - Product name: `item.free_field?.product_name`
     - Current price: `item.bid_status?.current_price`
     - View/favorite/bid counts: `item.attention_info`
     - Auction timing: `item.bid_status?.end_datetime`
     - Product details: `item.free_field` (maker, color, condition, etc.)

4. **Auction Status Logic:**

   - Implement dynamic CSS class assignment for the root `<li>` element based on auction state:
     - `"soldout"` - when `item.sold_out` is true
     - `"closed"` - when auction has ended
     - `"preauc"` - when auction hasn't started yet
     - `"extended"` - when `item.bid_status?.extending` is true
     - `""` (empty) - for active/new auctions

5. **Interactive Bidding Functionality:**

   - Reference implementation patterns from `auction-side/src/components/search-list/favorite/AuctionItem.vue`
   - Implement bid price input field with validation
   - Add bid-unit increment buttons (+10,000円, +50,000円, +100,000円)
   - Include bid submission button with proper validation
   - Use existing composables: `useBid()`, `useFavorite()`, and utility functions
   - Emit `@refresh` event after successful bid actions

6. **Technical Implementation:**

   - Use Vue 3 Composition API with `<script setup>`
   - Follow project's camelCase naming convention
   - Import and use existing utility functions: `priceLocaleString()`, `formatDateString()`, `conditionalNavigate()`
   - Ensure TypeScript compatibility and proper type definitions

7. **Fallback Handling:**
   - Provide graceful fallbacks for missing data fields
   - Display placeholder content when API data is unavailable
   - Maintain component stability with optional chaining (`?.`)

The goal is to transform this from a static demo component with multiple hardcoded items into a reusable, data-driven component that renders a single auction item with full interactive functionality.
