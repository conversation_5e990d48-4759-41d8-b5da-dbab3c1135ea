Task: Refactor Vue Component to Use Real Data

We need to refactor the Vue component below to **bind real data from the backend API instead of hardcoded HTML/CSS**.

---

### 📁 Component Details

- **Target file:** `auction-side/src/components/mypage/StandardAuctionItem.vue`
- **Used by:** `auction-side/src/components/mypage/_MyPage.vue`
- **Component prop:** `item: FormattedAuctionItem` (type defined in `auction-side/src/composables/_type.ts`)
- **Framework:** Vue 3 + Composition API + TypeScript
- **Authentication:** Cognito User Pool (idToken in `Authorization` header)
- **Tenant-based SPA with custom domains**
- **User info stored via Amplify in:** `auction-side/src/stores/cognitoAuth.js`

---

### ✅ Goals & Requirements

#### 1. **Analyze and Reuse Layout**

- Review the current HTML/CSS structure in `StandardAuctionItem.vue`
- Keep the existing layout/visuals as much as possible

#### 2. **Bind Real Auction Data**

- Use real data from the `public/search-auction-items` API
- Map fields such as:

  - `lot_id`, `item_no`, `manage_no`
  - `free_field.product_name`, `maker`, `color`, `capacity`, etc.
  - `bid_status.current_price`, `top_price`, `end_datetime`, etc.
  - `attention_info.view_count`, `favorited_count`, `is_favorited`
  - `sold_out` and other auction state flags

#### 3. **Handle Auction States**

- Implement logic for 5 auction states and apply corresponding `<li>` class:

  - `"extended"`: Extended auction
  - `""`: New auction
  - `"closed"`: Closed auction
  - `"soldout"`: Sold out
  - `"preauc"`: Pre-auction

> This can be implemented using a **computed property or method** that returns the correct CSS class based on item/bid status.

#### 4. **Conform to Project Standards**

- Follow the `camelCase` naming convention
- Use the existing `FormattedAuctionItem` type
- Do not break layout
- Use Composition API idioms and reactive references

---

### 📦 Sample API Response

```json
{
  "lot_id": "1",
  "manage_no": "ID0001",
  "sold_out": false,
  "bid_status": {
    "status": 0,
    "top_price": 0,
    "current_price": 4,
    "start_datetime": "2025-06-24T10:05:00+09:00",
    "end_datetime": "2025-07-05T10:05:00+09:00",
    "extending": false
  },
  "free_field": {
    "maker": "Apple",
    "product_name": "iPhone 11",
    "capacity": "256GB",
    "color": "黒",
    "rank": "C",
    "sim": "フリー"
  },
  "attention_info": {
    "view_count": 0,
    "favorited_count": 0,
    "is_favorited": false
  }
}
```

### 🧩 Optional Suggestions (if time allows)

- Display a placeholder or fallback UI if any key field is missing
- Support responsive layout if the existing UI already includes media queries
- Clean up any unused or obsolete code fragments in the old component
- Run "npm run @type" from auction-side directory to check typescript.
