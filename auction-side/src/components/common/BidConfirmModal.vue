<script setup>
  import termsJa from '@/assets/pdf/bid/利用規約_20250326.pdf'
  import termsEn from '@/assets/pdf/bid/terms_of_service_20250324.pdf'
  import ModalDialog from '../common/ModalDialog.vue'
  import {computed, ref, defineModel, watch} from 'vue'
  import {useBidConfirmStore} from '../../stores/bidConfirm'
  import {priceLocaleString} from '../../composables/common'
  import {useLocale} from 'vuetify'
  import {useAuthStore} from '../../stores/auth'
  import useApi from '../../composables/useApi'
  import {useMessageDialogStore} from '../../stores/messag-dialog'
  import {CLASSIFICATIONS, PATH_NAME} from '../../defined/const'
  import useSearchProducts from '../../composables/searchProducts'
  import useGetItemDetails from '../../composables/getItemDetails'
  import {useRoute} from 'vue-router'
  import {eventBus} from '../../utils'

  const emit = defineEmits(['refresh'])
  const props = defineProps(['isAscendingAuction'])

  const open = defineModel()

  const {t, current} = useLocale()
  const route = useRoute()
  const {apiExecute, parseHtmlResponseError} = useApi()
  const bid = useBidConfirmStore()
  const auth = useAuthStore()
  const messageDialog = useMessageDialogStore()
  const {search: searchList} = useSearchProducts()
  const {search: searchDetail} = useGetItemDetails()
  const hasBid = ref(false)
  // const agree = ref(false)

  const termsSrc = computed(() => {
    return current.value === 'ja' ? termsJa : termsEn
  })
  const showBidResult = computed(() => bid.showBidResult)
  const exhibitionList = computed(() => bid.data || [])
  const allExhTotalPrice = computed(() => {
    return priceLocaleString(exhibitionList.value.reduce((acc, exh) => acc + (exh?.bidTotalPrice || 0), 0))
  })

  const sendBid = async (inputParams = [{exhibitionItemNo: null, bidPrice: null, bidQuantity: null}]) => {
    const params = {
      bidItems: inputParams.map(({exhibitionItemNo, bidPrice, bidQuantity}) => ({
        exhibitionItemNo,
        bidPrice,
        bidQuantity,
      })),
    }
    await apiExecute('private/bid-items', params)
      .then(response => {
        bid.data.map(exh => {
          exh.bidList.map(item => {
            item.errorMessage = null
            const tmp = response?.bidList?.find(x => String(item.exhibitionItemNo) === String(x.exhibition_item_no))
            item.errorMessage = tmp?.errorMessage
          })
          exh.bidTotalPrice = exh.bidList.reduce((acc, item) => {
            if (item.errorMessage) {
              return acc
            }
            return acc + (item.bidTotalPrice || 0)
          }, 0)
        })
      })
      .catch(error => {
        const errMsg = parseHtmlResponseError(error)
        messageDialog.setShowMessage(errMsg, {isErr: true})
      })
  }

  const handleBid = async () => {
    if (!bid.agree) {
      return
    }
    auth.reloadCookies()
    if (auth.isAuthenticated) {
      const params = exhibitionList.value
        .map(exh => exh.bidList)
        .flat()
        .map(item => ({
          exhibitionItemNo: item.exhibitionItemNo,
          bidQuantity: item.bidQuantity,
          bidPrice: item.bidPrice,
        }))
      await sendBid(params)
      bid.setShowBidResult(true)
      hasBid.value = true
      eventBus.emit('onBidSuccess', params[0].exhibitionItemNo, params[0].bidPrice)
      // open.value = false
      // messageDialog.setShowMessage(t('productDetail.bidModal.bidSuccessFulMessage'), {
      //   name            : 'bid-success',
      //   showOkButton    : true,
      //   showCloseButton : false
      // })
    } else {
      open.value = false
      messageDialog.setShowMessage(t('productDetail.bidModal.loginRequiredMessage'), {
        showOkButton: true,
        showCloseButton: false,
      })
    }
  }

  const closeBidConfirmModal = () => {
    open.value = false
    if (hasBid.value) {
      const classification = props.isAscendingAuction ? CLASSIFICATIONS.ASCENDING : CLASSIFICATIONS.SEALED
      // Only emit 'refresh' if a bid was made and send isAscendingAuction as props to prevent switch to sealed tab 「ascending」 bid
      emit('refresh', classification)
      hasBid.value = false
    }
  }

  watch(
    () => messageDialog.clickedOk,
    async value => {
      auth.reloadCookies()
      if (value) {
        switch (messageDialog.dialogName) {
          case 'bid-success':
            if (route.path === PATH_NAME.FAVORITES) {
              searchList({favorite: true})
            } else if (route.path === PATH_NAME.BIDS) {
              searchList({bidding: true, unSoldOut: true})
            } else if (route.path === PATH_NAME.DETAIL) {
              await searchDetail(route.params.manageNo ?? '')
            }
            break
          default:
            break
        }
      }
      messageDialog.clickedOk = false
    }
  )
</script>
<template>
  <ModalDialog v-model="open" width="900px" @refresh-on-close="closeBidConfirmModal">
    <div>
      <!--ModalBid Start-->
      <div class="matching-dir-wrap" id="scrollable">
        <div v-for="exh in exhibitionList" :key="exh.exhibitionNo" class="auction-line-wrap">
          <p class="auction-ttl">{{ exh.exhibitionName }}</p>
          <ul class="bid-list">
            <li v-for="item in exh.bidList" :key="item.exhibitionItemNo">
              <p class="bid-item-name">
                <span class="label">{{ item.freeField?.maker }}</span>
                <!-- <span>:</span> -->
                <span>{{ item.freeField?.product_name }}</span>
              </p>
              <p>
                <span class="label">{{ t('productDetail.info.sim') }}</span
                ><span class="cont">{{ item.freeField?.sim }}</span>
              </p>
              <p>
                <span class="label">{{ t('productDetail.info.capacity') }}</span
                ><span class="cont">{{ item.freeField?.capacity }}</span>
              </p>
              <p>
                <span class="label">{{ t('productDetail.info.color') }}</span
                ><span class="cont">{{ item.freeField?.color }}</span>
              </p>
              <p>
                <span class="label">{{ t('productDetail.info.rank') }}</span
                ><span class="cont">{{ item.freeField?.rank }}</span>
              </p>
              <p v-if="!isAscendingAuction">
                <span class="label">{{ t('productDetail.bidQuantity') }}</span
                ><span class="cont">{{ priceLocaleString(item.bidQuantity) }}</span>
              </p>
              <p>
                <span class="label">{{ isAscendingAuction ? t('productDetail.bidPriceForAscAuction') : t('productDetail.bidUnitPrice') }}</span
                ><span class="cont">${{ priceLocaleString(item.bidPrice) }}</span>
              </p>
              <p v-if="!isAscendingAuction">
                <span class="label">{{ t('productDetail.bidTotalPrice') }}</span>
                <span class="cont">${{ priceLocaleString(item.bidTotalPrice) }}</span>
              </p>
              <div v-if="showBidResult" class="criterion">
                <dl
                  :class="{
                    success: !item.errorMessage,
                    failure: item.errorMessage,
                  }"
                >
                  <dt v-if="item.errorMessage">
                    {{ t('productDetail.errorMessages.bidFailed') }}
                  </dt>
                  <dt v-else>
                    {{ t('productDetail.errorMessages.bidSuccess') }}
                  </dt>
                  <dd>{{ item.errorMessage }}</dd>
                </dl>
              </div>
            </li>
          </ul>
          <div class="auction-total">
            <p class="label">{{ t('productDetail.bidModal.exhTotalPrice') }}</p>
            <p class="price">${{ priceLocaleString(exh.bidTotalPrice) }}</p>
          </div>
        </div>
        <div class="total-bid-amount">
          <p>{{ t('productDetail.bidModal.allExhTotalPrice') }}</p>
          <p class="price">${{ allExhTotalPrice }}</p>
        </div>
      </div>
      <template v-if="showBidResult">
        <div class="button-bid mt-5">
          <button class="btn bg-grey-lighten-1" @click="closeBidConfirmModal">
            {{ t('productDetail.bidModal.closeButton') }}
          </button>
        </div>
      </template>
      <template v-else>
        <div class="rule">
          <p class="tit-rule">{{ t('productDetail.bidModal.terms') }}</p>
          <iframe height="100%" width="100%" :src="termsSrc" tabindex="-1"></iframe>
          <div class="rule-check">
            <label for="rule-chk">
              <input type="checkbox" id="rule-chk" class="checkbox-input" required v-model="bid.agree" />
              <span class="checkbox-parts">{{ t('productDetail.bidModal.termsAgree') }}</span>
            </label>
          </div>
        </div>
        <p class="note-bid">{{ t('productDetail.bidModal.noteBid') }}</p>
        <div class="button-bid">
          <button class="btn" :disabled="!bid.agree" @click="handleBid">
            {{ t('productDetail.bidModal.bidButton') }}
          </button>
        </div>
      </template>
      <!--ModalBid Start-->
    </div>
  </ModalDialog>
</template>
