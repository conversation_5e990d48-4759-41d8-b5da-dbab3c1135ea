<script setup lang="ts">
  import type {FormattedAuctionItem} from '@/composables/_type'
  import useBid from '@/composables/bid'
  import {localeString2Number, priceLocaleString} from '@/composables/common'
  import {computed, reactive} from 'vue'
  import type {ProductListHandlers} from './types'

  /**
   * Props interface for ProductList component
   * Displays auction items that can be bid on - always in row-bid layout
   */
  interface Props {
    items: FormattedAuctionItem[]
    handlers?: ProductListHandlers
    customClasses?: string
  }

  const props = withDefaults(defineProps<Props>(), {
    customClasses: '',
  })

  // Initialize bid composable - get functions we actually use
  const {validateBidInput, bidHandle, refreshItems} = useBid()

  // Item-specific bid state management with error tracking
  // Maintains item-specific state while leveraging composable validation logic
  const itemBidStates = reactive<
    Record<
      string,
      {
        bidPrice: string
        bidQuantity: string
        validationErrors: {
          bidPrice: string | null
          bidQuantity: string | null
        }
      }
    >
  >({})

  // Initialize bid state for each item
  const initializeBidState = (item: FormattedAuctionItem) => {
    if (!itemBidStates[item.exhibition_item_no]) {
      itemBidStates[item.exhibition_item_no] = {
        bidPrice: item.bidPrice || '',
        bidQuantity: item.bidQuantity || '1',
        validationErrors: {
          bidPrice: null,
          bidQuantity: null,
        },
      }
    }
    return itemBidStates[item.exhibition_item_no]
  }

  // Get bid state for specific item
  const getBidState = (item: FormattedAuctionItem) => {
    return initializeBidState(item)
  }

  /**
   * Handle bidding unit button clicks using composable's addPitch logic
   * Adds the specified pitch amount to the current bid price for specific item
   */
  const handleAddPitch = (item: FormattedAuctionItem, pitch: number) => {
    const bidState = getBidState(item)
    const currentPrice = item.bid_status.current_price || item.free_field.start_price || 0

    // Use the same logic as the composable's addPitch function
    if (bidState.bidPrice) {
      bidState.bidPrice = String(localeString2Number(bidState.bidPrice) + pitch)
    } else {
      bidState.bidPrice = String(localeString2Number(currentPrice.toString()) + pitch)
    }

    // Format the price for display (keeping the same formatting as before)
    bidState.bidPrice = priceLocaleString(bidState.bidPrice, 10)

    // Clear any previous price errors
    bidState.validationErrors.bidPrice = null
  }

  /**
   * Validate bid input for a specific item using composable's validation logic
   */
  const validateItemBid = (item: FormattedAuctionItem) => {
    const bidState = getBidState(item)
    const isAscendingAuction = item.auction_classification === 1
    const currentBidPrice = item.bid_status.current_price?.toString() || '0'
    const lowestBidPrice = item.bid_status.minimum_bid_exceeded ? item.free_field.minimum_bid_price : item.free_field.start_price

    // Use the composable's validateBidInput function
    const validation = validateBidInput({
      inputBidPrice: bidState.bidPrice,
      inputBidQuantity: bidState.bidQuantity,
      currentBidPrice,
      enteredBidPrice: bidState.bidPrice,
      enteredBidQuantity: bidState.bidQuantity,
      lowestBidPrice,
      lowestBidQuantity: 1,
      pitchWidth: item.bid_status.pitch_width || 1,
      maxQuantity: 1, // Default quantity for auction items
      isAscendingAuction,
      hasUserBid: item.bid_status.is_top_member || false,
    })
    console.log('%c 🚞: validateItemBid -> validation ', 'font-size:16px;background-color:#7a33e0;color:white;', validation)

    // Store validation errors in item-specific state
    bidState.validationErrors.bidPrice = validation.bidPriceErr || null
    bidState.validationErrors.bidQuantity = validation.bidQuantityErr || null

    return !validation.bidPriceErr && !validation.bidQuantityErr
  }

  /**
   * Handle bid submission for a specific item using composable's bidHandle
   */
  const handleBidSubmission = (item: FormattedAuctionItem) => {
    console.log('%c 🌯: handleBidSubmission -> item ', 'font-size:16px;background-color:#29bbb2;color:white;', item)
    const bidState = getBidState(item)

    // Validate bid input first using our item-specific validation
    // console.log(
    //   '%c 🍚: handleBidSubmission -> validateItemBid(item) ',
    //   'font-size:16px;background-color:#49971e;color:white;',
    //   validateItemBid(item)
    // )
    if (!validateItemBid(item)) {
      return
    }

    // Prepare bid parameters for the composable's bidHandle function
    const bidParams = {
      exhibitionNo: item.exhibition_no,
      exhibitionItemNo: item.exhibition_item_no,
      exhibitionName: item.free_field.product_name || '',
      lowestBidPrice: item.free_field.minimum_bid_price || item.free_field.start_price || 0,
      lowestBidQuantity: 1,
      pitchWidth: item.bid_status.pitch_width || 1,
      freeField: item.free_field,
      newBidPrice: bidState.bidPrice,
      newBidQuantity: bidState.bidQuantity,
      currentBidPrice: item.bid_status.current_price?.toString() || '0',
      enteredBidPrice: bidState.bidPrice,
      enteredBidQuantity: bidState.bidQuantity,
      maxQuantity: 1, // Default quantity for auction items
      isAscendingAuction: item.auction_classification === 1,
      hasUserBid: item.bid_status.is_top_member || false,
    }
    // console.log(
    //   '%c 🇹🇬: handleBidSubmission -> bidParams ',
    //   'font-size:16px;background-color:#610fb4;color:white;',
    //   bidParams
    // )

    // Use the composable's bidHandle function directly
    bidHandle(bidParams)
  }

  const getItemLink = (item: FormattedAuctionItem) => {
    return item.link || '#'
  }

  const onItemClickHandler = (item: FormattedAuctionItem) => {
    if (props.handlers?.onItemClick) {
      props.handlers.onItemClick(item)
    }
  }

  const handleFavoriteToggle = async (exhibitionItemNo: string, currentFavorited: boolean) => {
    if (props.handlers?.onFavoriteToggle) {
      try {
        await props.handlers.onFavoriteToggle(exhibitionItemNo, currentFavorited)
      } catch (error) {
        console.error('Error toggling favorite:', error)
      }
    }
  }

  /**
   * Handle refresh using composable's refreshItems function
   */
  const handleRefresh = async () => {
    try {
      await refreshItems({refreshList: true})
    } catch (error) {
      console.error('Error refreshing with composable:', error)
    }
  }

  // Format price display
  const formatPrice = (price: number | string) => {
    if (typeof price === 'number') {
      return price.toLocaleString()
    }
    return price || '0'
  }

  // Get container classes - always row-bid for biddable items
  const containerClasses = computed(() => {
    const classes = ['item-list', 'row-bid']
    if (props.customClasses) {
      classes.push(props.customClasses)
    }
    return classes.join(' ')
  })

  // Get item classes
  const getItemClasses = (item: FormattedAuctionItem) => {
    const classes: string[] = ['box', 'auction-item']

    // Check for sold out status or auction ended
    const isAuctionEnded = new Date(item.end_datetime) < new Date()
    if (item.sold_out || item.status === 'soldout' || isAuctionEnded) {
      classes.push('soldout')
    }

    return classes.join(' ')
  }

  const isItemDisabled = (item: FormattedAuctionItem) => {
    const isAuctionEnded = new Date(item.end_datetime) < new Date()
    return item.sold_out || item.status === 'soldout' || isAuctionEnded
  }
</script>

<template>
  <!-- Biddable Products Container - Always row-bid layout -->
  <div v-if="props.items.length > 0" :class="containerClasses">
    <ul>
      <li v-for="item in props.items" :key="item.exhibition_item_no" :class="getItemClasses(item)">
        <figure>
          <!-- TODO: update real image -->
          <!-- <img :src="item.imgSrc" :alt="item.free_field.product_name" /> -->
          <img src="@/assets/img/stock/01.jpg" :alt="item.free_field.product_name" />
          <div class="tab-f">
            <span class="title-a">New</span>
          </div>
        </figure>

        <div class="item-p-desc">
          <p class="item-name">
            <a :href="getItemLink(item)" @click.prevent="onItemClickHandler(item)">
              {{ item.free_field.product_name }}
              <span v-if="item.free_field.shipping_free" class="tab-item">送料無料</span>
            </a>
          </p>

          <div class="desc-p-top">
            <div class="price-box">
              <p class="price">
                <span class="price-c">現在 : </span>
                <span class="price-v">{{ formatPrice(item.bid_status.current_price) }}</span>
                <span class="price-u">円</span>
              </p>
              <p v-if="item.free_field.instant_price" class="price">
                <span class="price-c">即決価格 : </span>
                <span class="price-v bl">{{ formatPrice(item.free_field.instant_price) }}</span>
                <span class="price-u bl">円</span>
              </p>
              <p class="price">
                <span class="price-c">最低入札価格 : </span>
                <span class="price-v bl sm">{{ formatPrice(item.free_field.minimum_bid_price || 0) }}</span>
                <span class="price-u bl sm">円</span>
              </p>
            </div>
            <ul class="tab-wrap-status">
              <li v-if="item.bid_status.is_top_member" class="top">あなたがTOP</li>
              <li v-if="item.bid_status.minimum_bid_exceeded" class="min-bid">最低落札超え</li>
            </ul>
          </div>

          <ul class="tab-wrap">
            <li class="tab-main">未使用に近い</li>
            <li class="tab-sub">ICON</li>
            <li class="tab-wari">ICON</li>
          </ul>

          <ul class="pre-bid">
            <li class="view">
              <p>{{ item.attention_info.view_count || 0 }}</p>
            </li>
            <li class="favo">
              <p>{{ item.attention_info.favorited_count || 0 }}</p>
            </li>
            <li class="bid-v">
              <p>{{ item.attention_info.bid_count }}</p>
            </li>
            <li class="end-v">
              <p>
                <span class="date red">{{ item.endDatePart }}</span>
                <span class="end">（{{ item.endTimePart }} 終了予定）</span>
              </p>
            </li>
          </ul>
          <button
            :class="['btn', 'favorite', 'row-bid', {active: item.attention_info.is_favorited}]"
            @click="handleFavoriteToggle(item.exhibition_item_no, !!item.attention_info.is_favorited)"
          ></button>
        </div>

        <!-- Full Bidding Interface for auction items -->
        <div class="place-bid" style="position: relative">
          <div class="price">
            <span class="ttl">入札価格</span>
            <input
              type="text"
              v-model="getBidState(item).bidPrice"
              class="price-bid"
              placeholder="1,000"
              :disabled="isItemDisabled(item)"
              :readonly="isItemDisabled(item)"
            />円
            <!-- Display validation error if exists -->
            <div v-if="getBidState(item).validationErrors.bidPrice" class="error-message" style="color: red; font-size: 12px; margin-top: 4px">
              {{ getBidState(item).validationErrors.bidPrice }}
            </div>
          </div>
          <ul class="bidding-unit">
            <li>
              <button class="bid-unit" :disabled="isItemDisabled(item)" @click="isItemDisabled(item) ? null : handleAddPitch(item, 10000)">
                <span class="icn_add"></span>¥10,000
              </button>
            </li>
            <li>
              <button class="bid-unit" :disabled="isItemDisabled(item)" @click="isItemDisabled(item) ? null : handleAddPitch(item, 50000)">
                <span class="icn_add"></span>¥50,000
              </button>
            </li>
            <li>
              <button class="bid-unit" :disabled="isItemDisabled(item)" @click="isItemDisabled(item) ? null : handleAddPitch(item, 100000)">
                <span class="icn_add"></span>¥100,000
              </button>
            </li>
          </ul>
          <div class="button-bid">
            <button class="btn" :disabled="isItemDisabled(item)" @click="isItemDisabled(item) ? null : handleBidSubmission(item)">
              <img class="pct" src="@/assets/img/common/icn_bid_w.svg" />
              <span class="bid">入札する</span>
            </button>
            <p class="update" :class="{disabled: isItemDisabled(item)}" @click="isItemDisabled(item) ? null : handleRefresh()">
              <span>更新</span>
            </p>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<style scoped>
  .section-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 2px solid #e0e0e0;
    color: #333;
  }

  .no-items {
    text-align: center;
    padding: 40px 20px;
    color: #666;
  }

  /* Overlay for sold out or ended auctions */
  #main #list .container .item-list.row-bid ul > li.soldout .place-bid::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(240, 240, 240, 0.8);
    z-index: 10;
    pointer-events: none; /* Prevent interaction with overlay */
  }

  /* Disable input and button interactions for sold out items */
  #main #list .container .item-list.row-bid ul > li.soldout .place-bid input,
  #main #list .container .item-list.row-bid ul > li.soldout .place-bid button {
    pointer-events: none;
    opacity: 0.6;
  }

  .price-bid {
    background-color: white;
  }
</style>
