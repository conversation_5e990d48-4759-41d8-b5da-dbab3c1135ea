<script setup>
  import {computed, onBeforeMount, reactive, watch} from 'vue'
  import {useRoute} from 'vue-router'
  import {useLocale} from 'vuetify'
  import useNotice from '../composables/useNotice'
  import {PATH_NAME} from '../defined/const'

  const route = useRoute()
  const {getNotices, notices} = useNotice()
  const notice = reactive({notice_no: null, title: '', sub_title: '', body: '', create_date: null})
  const {t: translate, current} = useLocale()

  const getData = async () => {
    await getNotices({
      limit: null,
    })
    const found = notices.find(item => item.notice_no === +route.params.noticeNo)
    if (found) {
      Object.assign(notice, found)
    }
  }

  onBeforeMount(async () => {
    await getData()
  })

  // 言語変更時にデータを再取得する
  watch(
    () => current.value,
    () => {
      getData()
    }
  )

  // 緊急お知らせ抽出
  const EmergencyNotificationList = computed(() => {
    return notices.filter(x => x.display_code === 3)
  })

  // Notice.notice_no が EmergencyNotificationList.notice_no と等しいことを確認する。
  const isEmergencyNotification = computed(() => {
    return EmergencyNotificationList.value.some(x => x.notice_no === notice.notice_no)
  })

  const getFile = key => {
    if (key) {
      window.open(import.meta.env.VITE_API_ENDPOINT.replace('api/', '') + encodeURIComponent(key), '_blank')
    }
  }
</script>

<template>
  <div>
    <div id="pNav" class="notice-detail">
      <ul>
        <li><router-link :to="PATH_NAME.TOP">TOP</router-link></li>
        <li>
          <router-link v-if="isEmergencyNotification" :to="PATH_NAME.NOTICE_LIST_IMPORTANT">
            {{ translate('notice.importantNoticeList') }}
          </router-link>
          <router-link v-else :to="PATH_NAME.NOTICE_LIST">
            {{ translate('notice.noticeList') }}
          </router-link>
        </li>
        <li>{{ notice.title }}</li>
      </ul>
    </div>
    <section id="static">
      <div class="container">
        <div class="news-detail-contents">
          <h2><span>NEW</span>{{ notice.title }}</h2>
          <p class="date">{{ notice.create_date }}</p>

          <!-- 通知本文をHTMLに変換 -->
          <div class="ql-editor" v-html="notice.body" style="padding-bottom: 0"></div>

          <!-- 添付ファイル -->
          <v-list density="compact" v-if="notice.file?.length > 0">
            <v-card-title>{{ translate('notice.attachedFile') }}</v-card-title>
            <v-list-item v-for="(file, index) in notice.file" :key="index" color="primary" @click="getFile(file)">
              <template v-slot:prepend>
                <v-icon icon="mdi-file-outline"></v-icon>
              </template>
              <v-list-item-title class="text-primary">
                {{ file.split('/').slice(-1).join() }}
              </v-list-item-title>
            </v-list-item>
          </v-list>

          <p class="back-list">
            <router-link :to="isEmergencyNotification ? PATH_NAME.NOTICE_LIST_IMPORTANT : PATH_NAME.NOTICE_LIST">
              {{ translate('common.backList') }}
            </router-link>
          </p>
        </div>

        <!-- <v-card>
          <v-card-title class="text-center text-h5 font-weight-bold">
            <span class="text-red font-weight-bold mr-3">NEW</span>{{ notice.title }}
          </v-card-title>
          <v-card-subtitle class="text-center">
            {{ notice.sub_title }}
          </v-card-subtitle>
          <v-card-text class="text-right text-subtitle-1">
            {{ notice.create_date }}
          </v-card-text>
          <v-card-text>
            <div class="ql-editor" v-html="notice.body" style="padding-bottom: 0"></div>
          </v-card-text>
          <v-card-item v-if="notice.file?.length > 0" style="padding-top: 0">
            <v-card-text style="padding: 0 0.5rem">添付ファイル</v-card-text>
            <v-card-actions style="padding-top: 0">
              <li v-for="(file, index) in notice.file" :key="index">
                <a class="custom-link" type="button" @click="getFile(file)">{{
                  file.split('/').slice(-1).join()
                }}</a>
              </li>
            </v-card-actions>
          </v-card-item>
          <v-card-actions>
            <v-spacer></v-spacer>
            <router-link
              :to="isEmergencyNotification ? PATH_NAME.NOTICE_LIST_IMPORTANT : PATH_NAME.NOTICE_LIST"
            >
              <v-btn size="large" color="primary" append-icon="mdi-chevron-right">一覧へ戻る</v-btn>
            </router-link>
          </v-card-actions>
        </v-card> -->
      </div>
    </section>
  </div>
</template>
<style src="../assets/css/quill.css"></style>
